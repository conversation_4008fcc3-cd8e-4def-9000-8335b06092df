import asyncio
import logging
from typing import List, Dict, Any
from datetime import datetime

from rich.console import Console
from rich.table import Table
from rich.progress import Progress, TaskID
from rich import print as rprint

from threads_client import ThreadsClient
from lead_scorer import LeadScorer
from models import ThreadsPost, ScoredPost, LeadScoringConfig
from config import config

logger = logging.getLogger(__name__)
console = Console()


class LeadGenerationWorkflow:
    """Main workflow orchestrator for lead generation from Threads."""
    
    def __init__(self):
        self.threads_client = ThreadsClient()
        self.lead_scorer = LeadScorer()
        self.scoring_config = config.get_lead_scoring_config()
    
    async def initialize(self):
        """Initialize the workflow components."""
        rprint("[bold blue]🚀 Initializing Lead Generation Workflow...[/bold blue]")
        
        # Test LM Studio connection
        if not await self.lead_scorer.test_connection():
            raise RuntimeError("Cannot connect to LM Studio. Please ensure it's running on the configured host.")
        
        # Attempt to login to Threads API
        await self.threads_client.login()
        
        rprint("[bold green]✅ Workflow initialized successfully![/bold green]")
    
    async def run_workflow(self, usernames: List[str], include_replies: bool = True) -> List[ScoredPost]:
        """Run the complete lead generation workflow."""
        
        rprint(f"[bold cyan]🔍 Starting lead generation for users: {', '.join(usernames)}[/bold cyan]")
        
        # Step 1: Fetch posts from Threads
        with console.status("[bold green]Fetching posts from Threads API..."):
            posts = await self.threads_client.search_posts(usernames, include_replies)
        
        if not posts:
            rprint("[bold red]❌ No posts found![/bold red]")
            return []
        
        rprint(f"[bold green]📝 Fetched {len(posts)} posts[/bold green]")
        
        # Step 2: Score posts with AI
        rprint("[bold cyan]🤖 Analyzing posts with Qwen3 model...[/bold cyan]")
        
        scored_posts = []
        
        with Progress() as progress:
            task = progress.add_task("[green]Scoring posts...", total=len(posts))
            
            # Process posts in batches
            batch_size = config.BATCH_SIZE
            for i in range(0, len(posts), batch_size):
                batch = posts[i:i + batch_size]
                scores = await self.lead_scorer.score_posts_batch(batch)
                
                for post, score in zip(batch, scores):
                    scored_post = ScoredPost(
                        post=post,
                        lead_score=score,
                        processed_at=datetime.now()
                    )
                    scored_posts.append(scored_post)
                
                progress.update(task, advance=len(batch))
        
        # Step 3: Filter and sort results
        leads = [sp for sp in scored_posts if sp.lead_score.is_lead and 
                sp.lead_score.general_score >= self.scoring_config.min_lead_score]
        
        # Sort by general score (highest first)
        leads.sort(key=lambda x: x.lead_score.general_score, reverse=True)
        
        rprint(f"[bold green]🎯 Found {len(leads)} potential leads out of {len(posts)} posts[/bold green]")
        
        return scored_posts
    
    def display_results(self, scored_posts: List[ScoredPost], show_all: bool = False):
        """Display the results in a formatted table."""
        
        # Filter to show only leads unless show_all is True
        if not show_all:
            posts_to_show = [sp for sp in scored_posts if sp.lead_score.is_lead and 
                           sp.lead_score.general_score >= self.scoring_config.min_lead_score]
            title = f"🎯 Potential Leads (Score ≥ {self.scoring_config.min_lead_score})"
        else:
            posts_to_show = scored_posts
            title = "📊 All Analyzed Posts"
        
        if not posts_to_show:
            rprint("[bold yellow]⚠️  No leads found matching the criteria[/bold yellow]")
            return
        
        # Create results table
        table = Table(title=title, show_header=True, header_style="bold magenta")
        table.add_column("Username", style="cyan", width=15)
        table.add_column("Caption", style="white", width=40)
        table.add_column("Urgency", justify="center", style="red", width=8)
        table.add_column("Obtain.", justify="center", style="yellow", width=8)
        table.add_column("Score", justify="center", style="green", width=8)
        table.add_column("Lead?", justify="center", style="bold", width=6)
        table.add_column("Likes", justify="center", style="blue", width=6)
        
        for scored_post in posts_to_show:
            post = scored_post.post
            score = scored_post.lead_score
            
            # Truncate caption for display
            caption = post.caption[:37] + "..." if len(post.caption) > 40 else post.caption
            
            # Color code the lead status
            lead_status = "✅ YES" if score.is_lead else "❌ NO"
            
            table.add_row(
                post.username,
                caption,
                str(score.urgency),
                str(score.obtainability),
                str(score.general_score),
                lead_status,
                str(post.like_count)
            )
        
        console.print(table)
        
        # Show detailed reasoning for top leads
        if not show_all and posts_to_show:
            rprint("\n[bold cyan]🔍 Top Lead Analysis:[/bold cyan]")
            for i, scored_post in enumerate(posts_to_show[:3], 1):
                rprint(f"\n[bold yellow]#{i} - @{scored_post.post.username}[/bold yellow]")
                rprint(f"[dim]Post:[/dim] {scored_post.post.caption[:100]}...")
                rprint(f"[dim]Reasoning:[/dim] {scored_post.lead_score.reasoning}")
                if scored_post.lead_score.keywords:
                    rprint(f"[dim]Keywords:[/dim] {', '.join(scored_post.lead_score.keywords)}")
    
    def export_results(self, scored_posts: List[ScoredPost], filename: str = None):
        """Export results to JSON file."""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"lead_analysis_{timestamp}.json"
        
        # Convert to serializable format
        export_data = {
            "timestamp": datetime.now().isoformat(),
            "total_posts": len(scored_posts),
            "leads_found": len([sp for sp in scored_posts if sp.lead_score.is_lead]),
            "config": self.scoring_config.dict(),
            "results": [sp.dict() for sp in scored_posts]
        }
        
        import json
        with open(filename, 'w') as f:
            json.dump(export_data, f, indent=2, default=str)
        
        rprint(f"[bold green]💾 Results exported to {filename}[/bold green]")
    
    async def cleanup(self):
        """Clean up resources."""
        await self.threads_client.close()
        rprint("[bold blue]🧹 Cleanup completed[/bold blue]")
