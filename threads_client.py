import asyncio
import logging
from typing import List, Optional
from datetime import datetime

from threads_api.src.threads_api import ThreadsAPI
from threads_api.src.http_sessions.aiohttp_session import AioHTTPSession
from threads_api.src.http_sessions.requests_session import RequestsSession
from threads_api.src.http_sessions.instagrapi_session import InstagrapiSession

from models import ThreadsPost
from config import config

logger = logging.getLogger(__name__)


class ThreadsClient:
    """Wrapper for Threads API to fetch posts for lead generation."""
    
    def __init__(self, http_session_class=AioHTTPSession):
        self.api = ThreadsAPI(http_session_class=http_session_class)
        self.authenticated = False
    
    async def login(self, username: Optional[str] = None, password: Optional[str] = None):
        """Login to Threads API if credentials are provided."""
        username = username or config.INSTAGRAM_USERNAME
        password = password or config.INSTAGRAM_PASSWORD
        
        if username and password:
            try:
                await self.api.login(username, password, cached_token_path=".token")
                self.authenticated = True
                logger.info("Successfully authenticated with Threads API")
            except Exception as e:
                logger.warning(f"Failed to authenticate: {e}. Continuing with public API only.")
                self.authenticated = False
        else:
            logger.info("No credentials provided. Using public API only.")
    
    async def get_user_posts(self, username: str, max_posts: Optional[int] = None) -> List[ThreadsPost]:
        """Get posts from a specific user."""
        max_posts = max_posts or config.MAX_POSTS_PER_USER
        posts = []
        
        try:
            # Get user ID from username
            user_id = await self.api.get_user_id_from_username(username)
            if not user_id:
                logger.error(f"Could not find user ID for username: {username}")
                return posts
            
            # Get user profile for additional context
            user_profile = None
            try:
                user_profile = await self.api.get_user_profile(user_id)
            except Exception as e:
                logger.warning(f"Could not fetch user profile for {username}: {e}")
            
            # Get user threads
            threads_response = await self.api.get_user_threads(user_id)
            
            for thread in threads_response.threads[:max_posts]:
                for thread_item in thread.thread_items:
                    if thread_item.post and thread_item.post.caption:
                        post = ThreadsPost(
                            post_id=thread_item.post.id,
                            username=username,
                            user_id=user_id,
                            caption=thread_item.post.caption.text,
                            like_count=thread_item.post.like_count or 0,
                            reply_count=getattr(thread_item.post, 'reply_count', 0),
                            repost_count=getattr(thread_item.post, 'repost_count', 0),
                            timestamp=datetime.now(),  # API doesn't provide timestamp in public mode
                            user_follower_count=user_profile.follower_count if user_profile else None,
                            user_bio=user_profile.biography if user_profile else None
                        )
                        posts.append(post)
            
            logger.info(f"Fetched {len(posts)} posts from user {username}")
            
        except Exception as e:
            logger.error(f"Error fetching posts for user {username}: {e}")
        
        return posts
    
    async def get_user_replies(self, username: str, max_posts: Optional[int] = None) -> List[ThreadsPost]:
        """Get replies from a specific user."""
        max_posts = max_posts or config.MAX_POSTS_PER_USER
        posts = []
        
        try:
            # Get user ID from username
            user_id = await self.api.get_user_id_from_username(username)
            if not user_id:
                logger.error(f"Could not find user ID for username: {username}")
                return posts
            
            # Get user profile for additional context
            user_profile = None
            try:
                user_profile = await self.api.get_user_profile(user_id)
            except Exception as e:
                logger.warning(f"Could not fetch user profile for {username}: {e}")
            
            # Get user replies
            replies_response = await self.api.get_user_replies(user_id)
            
            for thread in replies_response.threads[:max_posts]:
                if len(thread.thread_items) > 1:
                    # The reply is usually the second item
                    reply_item = thread.thread_items[1]
                    if reply_item.post and reply_item.post.caption:
                        post = ThreadsPost(
                            post_id=reply_item.post.id,
                            username=username,
                            user_id=user_id,
                            caption=reply_item.post.caption.text,
                            like_count=reply_item.post.like_count or 0,
                            reply_count=getattr(reply_item.post, 'reply_count', 0),
                            repost_count=getattr(reply_item.post, 'repost_count', 0),
                            timestamp=datetime.now(),
                            user_follower_count=user_profile.follower_count if user_profile else None,
                            user_bio=user_profile.biography if user_profile else None
                        )
                        posts.append(post)
            
            logger.info(f"Fetched {len(posts)} replies from user {username}")
            
        except Exception as e:
            logger.error(f"Error fetching replies for user {username}: {e}")
        
        return posts
    
    async def search_posts(self, usernames: List[str], include_replies: bool = True) -> List[ThreadsPost]:
        """Search posts from multiple users."""
        all_posts = []
        
        for username in usernames:
            # Get main posts
            posts = await self.get_user_posts(username)
            all_posts.extend(posts)
            
            # Get replies if requested
            if include_replies:
                replies = await self.get_user_replies(username)
                all_posts.extend(replies)
            
            # Add small delay to be respectful to the API
            await asyncio.sleep(1)
        
        logger.info(f"Total posts collected: {len(all_posts)}")
        return all_posts
    
    async def close(self):
        """Close the API connection gracefully."""
        await self.api.close_gracefully()
