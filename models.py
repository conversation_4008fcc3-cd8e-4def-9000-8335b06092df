from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime


class ThreadsPost(BaseModel):
    """Represents a Threads post with relevant information for lead scoring."""
    
    post_id: str
    username: str
    user_id: str
    caption: str
    like_count: int = 0
    reply_count: int = 0
    repost_count: int = 0
    timestamp: Optional[datetime] = None
    url: Optional[str] = None
    user_follower_count: Optional[int] = None
    user_bio: Optional[str] = None


class LeadScore(BaseModel):
    """Represents the AI-generated lead scoring for a post."""
    
    urgency: int = Field(..., ge=0, le=100, description="How time-sensitive the need appears (0-100)")
    obtainability: int = Field(..., ge=0, le=100, description="How likely we are to convert this lead (0-100)")
    general_score: int = Field(..., ge=0, le=100, description="Overall assessment of lead quality (0-100)")
    reasoning: str = Field(..., description="AI explanation for the scores")
    is_lead: bool = Field(..., description="Whether this post represents a potential lead")
    keywords: List[str] = Field(default_factory=list, description="Key terms that influenced the scoring")


class ScoredPost(BaseModel):
    """Combines a Threads post with its lead scoring."""
    
    post: ThreadsPost
    lead_score: LeadScore
    processed_at: datetime = Field(default_factory=datetime.now)


class LeadScoringConfig(BaseModel):
    """Configuration for lead scoring parameters."""
    
    min_lead_score: int = Field(default=70, ge=0, le=100)
    urgency_weight: float = Field(default=0.3, ge=0, le=1)
    obtainability_weight: float = Field(default=0.3, ge=0, le=1)
    general_weight: float = Field(default=0.4, ge=0, le=1)
    keywords_to_watch: List[str] = Field(default_factory=lambda: [
        "need", "urgent", "help", "looking for", "hiring", "project", 
        "budget", "deadline", "asap", "immediately", "contract", "freelance"
    ])
