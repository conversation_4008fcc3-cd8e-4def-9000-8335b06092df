import os
from dotenv import load_dotenv
from models import LeadScoringConfig

# Load environment variables
load_dotenv()


class Config:
    """Application configuration."""
    
    # Threads API Configuration
    INSTAGRAM_USERNAME = os.getenv("INSTAGRAM_USERNAME")
    INSTAGRAM_PASSWORD = os.getenv("INSTAGRAM_PASSWORD")
    
    # LM Studio Configuration
    LM_STUDIO_HOST = os.getenv("LM_STUDIO_HOST", "http://localhost:1234")
    LM_STUDIO_MODEL = os.getenv("LM_STUDIO_MODEL", "qwen2.5-3b-instruct")
    
    # Lead Scoring Configuration
    MIN_LEAD_SCORE = int(os.getenv("MIN_LEAD_SCORE", "70"))
    BATCH_SIZE = int(os.getenv("BATCH_SIZE", "10"))
    MAX_POSTS_PER_USER = int(os.getenv("MAX_POSTS_PER_USER", "50"))
    
    # Logging
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    
    @classmethod
    def get_lead_scoring_config(cls) -> LeadScoringConfig:
        """Get lead scoring configuration."""
        return LeadScoringConfig(
            min_lead_score=cls.MIN_LEAD_SCORE
        )


# Global config instance
config = Config()
