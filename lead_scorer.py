import json
import logging
import async<PERSON>
from typing import List, Dict, Any
import httpx
from openai import AsyncOpenAI

from models import ThreadsPost, LeadScore, LeadScoringConfig
from config import config

logger = logging.getLogger(__name__)


class LeadScorer:
    """AI-powered lead scoring using local Qwen3 model via LM Studio."""
    
    def __init__(self, scoring_config: LeadScoringConfig = None):
        self.scoring_config = scoring_config or config.get_lead_scoring_config()
        
        # Initialize OpenAI client pointing to LM Studio
        self.client = AsyncOpenAI(
            base_url=f"{config.LM_STUDIO_HOST}/v1",
            api_key="lm-studio"  # LM Studio doesn't require a real API key
        )
        
        self.model_name = config.LM_STUDIO_MODEL
    
    async def test_connection(self) -> bool:
        """Test if LM Studio is running and accessible."""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{config.LM_STUDIO_HOST}/v1/models")
                if response.status_code == 200:
                    models = response.json()
                    logger.info(f"LM Studio is running. Available models: {[m['id'] for m in models.get('data', [])]}")
                    return True
                else:
                    logger.error(f"LM Studio responded with status {response.status_code}")
                    return False
        except Exception as e:
            logger.error(f"Failed to connect to LM Studio: {e}")
            return False
    
    def _create_scoring_prompt(self, post: ThreadsPost) -> str:
        """Create a prompt for the AI model to score the post."""
        
        prompt = f"""You are an expert lead generation analyst. Analyze the following social media post and determine if it represents a potential business lead.

POST DETAILS:
- Username: {post.username}
- Caption: "{post.caption}"
- Likes: {post.like_count}
- User Bio: {post.user_bio or "Not available"}
- Follower Count: {post.user_follower_count or "Not available"}

SCORING CRITERIA:
1. URGENCY (0-100): How time-sensitive is the need expressed? Look for words like "urgent", "ASAP", "deadline", "immediately", etc.
2. OBTAINABILITY (0-100): How likely are we to convert this into a business opportunity? Consider factors like budget mentions, specific requirements, professional tone.
3. GENERAL_SCORE (0-100): Overall assessment of lead quality considering business potential, clarity of need, and likelihood of conversion.

KEYWORDS TO WATCH FOR: {', '.join(self.scoring_config.keywords_to_watch)}

Respond with a JSON object in this exact format:
{{
    "is_lead": true/false,
    "urgency": 0-100,
    "obtainability": 0-100,
    "general_score": 0-100,
    "reasoning": "Detailed explanation of your scoring decision",
    "keywords": ["list", "of", "relevant", "keywords", "found"]
}}

Be thorough in your analysis and provide specific reasoning for your scores."""
        
        return prompt
    
    async def score_post(self, post: ThreadsPost) -> LeadScore:
        """Score a single post using the AI model."""
        try:
            prompt = self._create_scoring_prompt(post)
            
            response = await self.client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert lead generation analyst. Always respond with valid JSON only."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.3,
                max_tokens=500
            )
            
            # Parse the AI response
            ai_response = response.choices[0].message.content.strip()
            
            # Try to extract JSON from the response
            try:
                # Sometimes the model wraps JSON in markdown code blocks
                if "```json" in ai_response:
                    ai_response = ai_response.split("```json")[1].split("```")[0].strip()
                elif "```" in ai_response:
                    ai_response = ai_response.split("```")[1].strip()
                
                result = json.loads(ai_response)
                
                # Validate and create LeadScore object
                lead_score = LeadScore(
                    urgency=max(0, min(100, int(result.get("urgency", 0)))),
                    obtainability=max(0, min(100, int(result.get("obtainability", 0)))),
                    general_score=max(0, min(100, int(result.get("general_score", 0)))),
                    reasoning=result.get("reasoning", "No reasoning provided"),
                    is_lead=bool(result.get("is_lead", False)),
                    keywords=result.get("keywords", [])
                )
                
                logger.debug(f"Scored post {post.post_id}: {lead_score.general_score}/100")
                return lead_score
                
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse AI response as JSON: {e}")
                logger.error(f"AI Response: {ai_response}")
                
                # Return a default low score if parsing fails
                return LeadScore(
                    urgency=0,
                    obtainability=0,
                    general_score=0,
                    reasoning=f"Failed to parse AI response: {str(e)}",
                    is_lead=False,
                    keywords=[]
                )
                
        except Exception as e:
            logger.error(f"Error scoring post {post.post_id}: {e}")
            return LeadScore(
                urgency=0,
                obtainability=0,
                general_score=0,
                reasoning=f"Error during scoring: {str(e)}",
                is_lead=False,
                keywords=[]
            )
    
    async def score_posts_batch(self, posts: List[ThreadsPost]) -> List[LeadScore]:
        """Score multiple posts in parallel with rate limiting."""
        semaphore = asyncio.Semaphore(3)  # Limit concurrent requests
        
        async def score_with_semaphore(post):
            async with semaphore:
                return await self.score_post(post)
        
        tasks = [score_with_semaphore(post) for post in posts]
        scores = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle any exceptions that occurred
        valid_scores = []
        for i, score in enumerate(scores):
            if isinstance(score, Exception):
                logger.error(f"Exception scoring post {posts[i].post_id}: {score}")
                # Create a default score for failed posts
                valid_scores.append(LeadScore(
                    urgency=0,
                    obtainability=0,
                    general_score=0,
                    reasoning=f"Exception during scoring: {str(score)}",
                    is_lead=False,
                    keywords=[]
                ))
            else:
                valid_scores.append(score)
        
        return valid_scores
