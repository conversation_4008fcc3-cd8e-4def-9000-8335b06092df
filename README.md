# 🧵 Threads Lead Generator 🎯

An intelligent lead generation tool that pulls posts from Meta's Threads API and analyzes them using a local Qwen3 model via LM Studio to identify potential business leads.

## Features

- 🔍 **Smart Post Analysis**: Fetches posts and replies from specified Threads users
- 🤖 **AI-Powered Scoring**: Uses local Qwen3 model to score posts on:
  - **Urgency** (0-100): How time-sensitive the need appears
  - **Obtainability** (0-100): Likelihood of converting the lead
  - **General Score** (0-100): Overall lead quality assessment
- 📊 **Rich Output**: Beautiful terminal interface with detailed results
- 💾 **Export Capability**: Save results to JSON for further analysis
- ⚡ **Async Processing**: Fast parallel processing of multiple posts

## Prerequisites

1. **LM Studio** running locally with Qwen3 model loaded
2. **Python 3.8+**
3. **Optional**: Instagram credentials for authenticated Threads API access

## Quick Start

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Start LM Studio**:
   - Download and install [LM Studio](https://lmstudio.ai/)
   - Load a Qwen3 model (e.g., `qwen2.5-3b-instruct`)
   - Start the local server (default: `http://localhost:1234`)

3. **Configure environment** (optional):
   ```bash
   cp .env.example .env
   # Edit .env with your settings
   ```

4. **Run the tool**:
   ```bash
   python main.py zuck elonmusk --show-all
   ```

## Usage Examples

### Basic Usage
```bash
# Analyze posts from specific users
python main.py zuck elonmusk

# Include replies in analysis
python main.py zuck --no-replies

# Show all posts, not just leads
python main.py zuck --show-all

# Set custom minimum lead score
python main.py zuck --min-score 80

# Export results to JSON
python main.py zuck --export results.json
```

### Advanced Usage
```bash
# Analyze multiple users with custom settings
python main.py user1 user2 user3 --min-score 75 --export --show-all
```

## Configuration

### Environment Variables (.env)

```bash
# LM Studio Configuration
LM_STUDIO_HOST=http://localhost:1234
LM_STUDIO_MODEL=qwen2.5-3b-instruct

# Threads API (optional - for authenticated access)
INSTAGRAM_USERNAME=your_username
INSTAGRAM_PASSWORD=your_password

# Lead Scoring
MIN_LEAD_SCORE=70
BATCH_SIZE=10
MAX_POSTS_PER_USER=50

# Logging
LOG_LEVEL=INFO
```

### Scoring Criteria

The AI model evaluates posts based on:

- **Keywords**: "need", "urgent", "help", "looking for", "hiring", "project", "budget", "deadline", "asap", "immediately", "contract", "freelance"
- **Context**: User bio, follower count, engagement metrics
- **Content Analysis**: Tone, specificity, business intent

## Output

The tool provides:

1. **Terminal Display**: Rich formatted table with scores and analysis
2. **Detailed Reasoning**: AI explanation for top leads
3. **JSON Export**: Complete results with metadata
4. **Logs**: Detailed logging for debugging

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Threads API   │───▶│  Post Fetcher    │───▶│  Lead Scorer    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Results UI    │◀───│   Workflow       │◀───│   LM Studio     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Troubleshooting

### LM Studio Connection Issues
- Ensure LM Studio is running on the correct port
- Check that a Qwen3 model is loaded
- Verify the model name in configuration

### Threads API Issues
- Public API works without authentication
- For better rate limits, add Instagram credentials
- Some features require authentication

### Performance Tips
- Adjust `BATCH_SIZE` for your system capabilities
- Use `--no-replies` to focus on main posts only
- Set appropriate `MAX_POSTS_PER_USER` limits

## License

MIT License - see LICENSE file for details.
