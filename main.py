#!/usr/bin/env python3
"""
Threads Lead Generation Tool

This tool pulls posts from the Threads API, analyzes them with a local Qwen3 model
via LM Studio, and scores them as potential leads based on urgency, obtainability,
and general lead quality.
"""

import asyncio
import logging
import argparse
from typing import List

from rich.console import Console
from rich import print as rprint

from workflow import LeadGenerationWorkflow
from config import config

console = Console()


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=getattr(logging, config.LOG_LEVEL.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('lead_generation.log')
        ]
    )


async def main():
    """Main entry point for the lead generation workflow."""
    
    parser = argparse.ArgumentParser(description="Threads Lead Generation Tool")
    parser.add_argument(
        "usernames", 
        nargs="+", 
        help="Threads usernames to analyze (e.g., 'zuck' 'elonmusk')"
    )
    parser.add_argument(
        "--no-replies", 
        action="store_true", 
        help="Skip analyzing user replies, only analyze main posts"
    )
    parser.add_argument(
        "--show-all", 
        action="store_true", 
        help="Show all analyzed posts, not just leads"
    )
    parser.add_argument(
        "--export", 
        type=str, 
        help="Export results to JSON file (optional filename)"
    )
    parser.add_argument(
        "--min-score", 
        type=int, 
        default=config.MIN_LEAD_SCORE,
        help=f"Minimum lead score to display (default: {config.MIN_LEAD_SCORE})"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging()
    
    # Display banner
    rprint("""
[bold cyan]
╔══════════════════════════════════════════════════════════════╗
║                    🧵 THREADS LEAD GENERATOR 🎯                ║
║                                                              ║
║  Powered by Qwen3 via LM Studio for intelligent lead scoring ║
╚══════════════════════════════════════════════════════════════╝
[/bold cyan]
    """)
    
    # Initialize workflow
    workflow = LeadGenerationWorkflow()
    
    try:
        # Initialize components
        await workflow.initialize()
        
        # Update minimum score if provided
        if args.min_score != config.MIN_LEAD_SCORE:
            workflow.scoring_config.min_lead_score = args.min_score
            rprint(f"[yellow]📊 Using custom minimum lead score: {args.min_score}[/yellow]")
        
        # Run the workflow
        include_replies = not args.no_replies
        scored_posts = await workflow.run_workflow(
            usernames=args.usernames,
            include_replies=include_replies
        )
        
        if scored_posts:
            # Display results
            workflow.display_results(scored_posts, show_all=args.show_all)
            
            # Export if requested
            if args.export is not None:
                filename = args.export if args.export else None
                workflow.export_results(scored_posts, filename)
            
            # Summary statistics
            total_posts = len(scored_posts)
            leads = [sp for sp in scored_posts if sp.lead_score.is_lead and 
                    sp.lead_score.general_score >= workflow.scoring_config.min_lead_score]
            
            rprint(f"\n[bold green]📈 Summary:[/bold green]")
            rprint(f"  • Total posts analyzed: {total_posts}")
            rprint(f"  • Potential leads found: {len(leads)}")
            rprint(f"  • Lead conversion rate: {len(leads)/total_posts*100:.1f}%")
            
            if leads:
                avg_score = sum(sp.lead_score.general_score for sp in leads) / len(leads)
                rprint(f"  • Average lead score: {avg_score:.1f}/100")
        
    except KeyboardInterrupt:
        rprint("\n[bold yellow]⚠️  Workflow interrupted by user[/bold yellow]")
    except Exception as e:
        rprint(f"\n[bold red]❌ Error: {e}[/bold red]")
        logging.exception("Workflow failed")
    finally:
        # Cleanup
        await workflow.cleanup()


def cli():
    """CLI entry point."""
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        rprint("\n[bold yellow]👋 Goodbye![/bold yellow]")


if __name__ == "__main__":
    cli()
